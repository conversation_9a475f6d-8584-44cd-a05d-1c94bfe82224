{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "node",
            "request": "launch",
            "name": "深度调试 (包含 node_modules)",
            "skipFiles": [],
            "program": "${workspaceFolder}\\src\\cli.ts",
            "args": [
                "start"
            ],
            "runtimeExecutable": "npx",
            "runtimeArgs": [
                "tsx"
            ],
            "env": {
                "NODE_ENV": "development",
                "LOG": "true"
            },
            "console": "integratedTerminal",
            "smartStep": false,
            "stopOnEntry": false,
            "sourceMaps": true,
            "autoAttachChildProcesses": true,
            "resolveSourceMapLocations": [
                "**",
                "!**/node_modules/**",
                "${workspaceFolder}/node_modules/@musistudio/**"
            ],
            "outFiles": [
                "${workspaceFolder}/dist/**/*.js",
                "${workspaceFolder}/node_modules/**/*.js"
            ]
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Node.js 原生调试 (支持所有断点)",
            "program": "${workspaceFolder}\\src\\cli.ts",
            "args": [
                "start"
            ],
            "runtimeArgs": [
                "--loader",
                "tsx/esm"
            ],
            "env": {
                "NODE_ENV": "development",
                "LOG": "true"
            },
            "console": "integratedTerminal",
            "skipFiles": [],
            "sourceMaps": true
        },
        {
            "type": "node",
            "request": "launch",
            "name": "简单调试 (无限制)",
            "program": "${workspaceFolder}\\src\\cli.ts",
            "args": [
                "start"
            ],
            "runtimeExecutable": "node",
            "runtimeArgs": [
                "--inspect-brk",
                "--loader",
                "tsx/esm"
            ],
            "env": {
                "NODE_ENV": "development",
                "LOG": "true"
            },
            "console": "integratedTerminal"
        }
    ]
}
