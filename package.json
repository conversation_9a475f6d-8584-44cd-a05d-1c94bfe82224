{"name": "@musistudio/claude-code-router", "version": "1.0.20", "description": "Use Claude Code without an Anthropics account and route it to another LLM provider", "bin": {"ccr": "./dist/cli.js"}, "scripts": {"build": "esbuild src/cli.ts --bundle --platform=node --outfile=dist/cli.js && shx cp node_modules/tiktoken/tiktoken_bg.wasm dist/tiktoken_bg.wasm", "dev": "tsx watch src/cli.ts start", "dev:code": "tsx watch src/cli.ts code", "start": "node dist/cli.js start", "start:dev": "tsx src/cli.ts start", "debug": "tsx --inspect-brk src/cli.ts start", "debug:code": "tsx --inspect-brk src/cli.ts code", "test": "tsx src/cli.ts status", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["claude", "code", "router", "llm", "anthropic"], "author": "musistudio", "license": "MIT", "dependencies": {"@musistudio/llms": "^1.0.7", "dotenv": "^16.4.7", "tiktoken": "^1.0.21", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^24.0.14", "esbuild": "^0.25.1", "fastify": "^5.4.0", "shx": "^0.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "tsx": "^4.7.0", "rimraf": "^5.0.0", "nodemon": "^3.0.0", "concurrently": "^8.0.0", "@types/uuid": "^9.0.0"}, "publishConfig": {"ignore": ["!build/", "src/", "screenshots/"]}}