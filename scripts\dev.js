#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// 设置开发环境变量
process.env.NODE_ENV = 'development';
process.env.LOG = 'true';

// 获取命令行参数
const args = process.argv.slice(2);
const command = args[0] || 'start';

console.log('🚀 Starting Claude Code Router in development mode...');
console.log(`📝 Command: ${command}`);
console.log(`🌍 Environment: ${process.env.NODE_ENV}`);

// 启动 tsx 进程
const tsxProcess = spawn('npx', ['tsx', 'watch', 'src/cli.ts', command], {
  stdio: 'inherit',
  shell: true,
  env: {
    ...process.env,
    FORCE_COLOR: '1' // 保持颜色输出
  }
});

// 处理进程退出
tsxProcess.on('close', (code) => {
  console.log(`\n📊 Development server exited with code ${code}`);
  process.exit(code);
});

// 处理 Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down development server...');
  tsxProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Terminating development server...');
  tsxProcess.kill('SIGTERM');
});