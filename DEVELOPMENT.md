# 开发指南

## 快速开始

### 1. 安装依赖
```bash
npm install
# 或者
pnpm install
```

### 2. 配置开发环境
```bash
# 复制并编辑开发配置
cp .env.development .env.local

# 创建配置文件
mkdir -p ~/.claude-code-router
cp config.example.json ~/.claude-code-router/config.json
# 编辑配置文件，添加你的 API Key
```

### 3. 开发运行

#### 启动开发服务器
```bash
npm run dev
# 或者
npm run start:dev
```

#### 测试 code 命令
```bash
npm run dev:code "Write hello world"
```

#### 调试模式
```bash
npm run debug
# 然后在 Chrome 中访问 chrome://inspect
```

## 开发脚本说明

- `npm run dev` - 启动开发服务器（带文件监听）
- `npm run dev:code` - 开发模式下测试 code 命令
- `npm run start:dev` - 直接启动开发服务器（不监听文件变化）
- `npm run debug` - 启动调试模式
- `npm run debug:code` - 调试 code 命令
- `npm run build` - 构建生产版本
- `npm run start` - 运行生产版本
- `npm run test` - 测试服务状态
- `npm run type-check` - TypeScript 类型检查
- `npm run clean` - 清理构建文件

## 开发工作流

### 1. 启动开发服务器
```bash
npm run dev
```

### 2. 在另一个终端测试
```bash
# 检查服务状态
npm run test

# 测试 API
curl -X POST http://localhost:3456/v1/messages \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet",
    "messages": [{"role": "user", "content": "Hello"}]
  }'

# 测试 code 命令
npm run dev:code "Write a Python hello world"
```

### 3. 调试
```bash
# 启动调试模式
npm run debug

# 在 Chrome 中打开 chrome://inspect
# 点击 "Open dedicated DevTools for Node"
```

## 文件结构

```
src/
├── cli.ts              # CLI 入口
├── index.ts            # 主应用逻辑
├── server.ts           # 服务器创建
├── constants.ts        # 常量定义
├── middleware/
│   └── auth.ts         # 认证中间件
└── utils/
    ├── router.ts       # 智能路由
    ├── index.ts        # 工具函数
    ├── codeCommand.ts  # Code 命令处理
    ├── processCheck.ts # 进程管理
    ├── status.ts       # 状态检查
    ├── close.ts        # 服务关闭
    └── log.ts          # 日志工具
```

## 配置文件

### ~/.claude-code-router/config.json
```json
{
  "LOG": true,
  "Providers": [
    {
      "name": "deepseek",
      "api_base_url": "https://api.deepseek.com/chat/completions",
      "api_key": "sk-your-key",
      "models": ["deepseek-chat"],
      "transformer": {
        "use": ["deepseek"]
      }
    }
  ],
  "Router": {
    "default": "deepseek,deepseek-chat"
  }
}
```

## 常见问题

### 1. 端口被占用
```bash
# 检查端口占用
lsof -i :3456  # macOS/Linux
netstat -ano | findstr :3456  # Windows

# 使用不同端口
PORT=3457 npm run dev
```

### 2. TypeScript 错误
```bash
# 跳过类型检查
npm run start:dev

# 或者修复类型错误
npm run type-check
```

### 3. 配置文件问题
```bash
# 验证配置文件
cat ~/.claude-code-router/config.json | jq .

# 重新创建配置
rm ~/.claude-code-router/config.json
npm run dev  # 会进入交互式配置
```

## 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建 Pull Request

## 调试技巧

### 1. 添加调试日志
```typescript
// 在代码中添加
console.log('🐛 Debug:', variable);
```

### 2. 使用 Chrome DevTools
```bash
npm run debug
# 在 Chrome 中设置断点调试
```

### 3. 查看日志文件
```bash
tail -f ~/.claude-code-router/claude-code-router.log
```